#!/usr/bin/env node
/**
 * Simple CLI to convert a communication JSON to Markdown.
 * - Reads a JSON file with a `messages` array containing { role, content }
 * - Writes a Markdown file with sections per message
 * - Replaces all "\n" (LF) with Windows CRLF in the output
 *
 * Usage:
 *   node comm2md.js [input_json] [output_md]
 * Defaults:
 *   input_json: ./communication.json
 *   output_md:  ./communication.md
 */

const fs = require('fs');
const path = require('path');

function readJson(filePath) {
  try {
    const raw = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(raw);
  } catch (err) {
    console.error(`[Error] Failed to read or parse JSON: ${filePath}`);
    console.error(err.message);
    process.exit(1);
  }
}

function extractTextBlocks(content) {
  // Returns an array of textual segments to render as separate code blocks
  const out = [];
  const takeIfString = (val) => {
    if (typeof val === 'string') out.push(val);
  };

  const tryObject = (obj) => {
    if (!obj || typeof obj !== 'object') return;
    // Prefer explicit 'text'
    if (typeof obj.text === 'string') {
      out.push(obj.text);
      return;
    }
    // Fallback: 'content' if it's a plain string and has no obvious sub-structure
    if (typeof obj.content === 'string') {
      const keys = Object.keys(obj).filter((k) => k !== 'content');
      if (keys.length === 0) {
        out.push(obj.content);
        return;
      }
    }
  };

  if (typeof content === 'string') {
    out.push(content);
  } else if (Array.isArray(content)) {
    for (const item of content) {
      if (typeof item === 'string') {
        out.push(item);
      } else if (item && typeof item === 'object') {
        tryObject(item);
      }
    }
  } else if (content && typeof content === 'object') {
    tryObject(content);
  }

  return out;
}

function detectFenceLang(text) {
  // Classify as XML only if the entire content is wrapped by a matching root tag
  // e.g., <root ...> ... </root>
  const s = String(text || '').trim();
  const m = s.match(/^<([A-Za-z_][\w\-.:]*)\b[^>]*>[\s\S]*<\/\1>$/);
  if (m) return 'xml';
  return 'md';
}

function toMarkdown(doc) {
  const lines = [];

  const model = doc && doc.model ? String(doc.model) : undefined;
  if (model) {
    lines.push(`# Kommunikation (Modell: ${model})`);
  } else {
    lines.push('# Kommunikation');
  }
  lines.push('');

  const msgs = Array.isArray(doc?.messages) ? doc.messages : [];
  msgs.forEach((msg, idx) => {
    const role = msg && msg.role ? String(msg.role) : 'unknown';
    const blocks = extractTextBlocks(msg ? msg.content : '');

    lines.push(`## ${idx + 1}. Rolle: ${role}`);
    lines.push('');
    if (blocks.length === 0) {
      // Fallback: show raw JSON of message content
      lines.push('```md');
      lines.push(String(typeof msg?.content === 'undefined' ? '' : (typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content, null, 2))));
      lines.push('```');
      lines.push('');
    } else {
      for (const b of blocks) {
        const lang = detectFenceLang(b);
        lines.push(`\`\`\`${lang}`);
        lines.push(b);
        lines.push('```');
        lines.push('');
      }
    }
  });

  let md = lines.join('\n');
  // Replace LF with CRLF as requested
  md = md.replace(/\n/g, '\r\n');
  return md;
}

function main() {
  const input = process.argv[2] || path.join(__dirname, 'communication.json');
  const output = process.argv[3] || path.join(__dirname, 'communication.md');

  const doc = readJson(input);
  const md = toMarkdown(doc);

  try {
    fs.writeFileSync(output, md, 'utf8');
    console.log(`[OK] Markdown erzeugt: ${output}`);
  } catch (err) {
    console.error(`[Error] Failed to write output: ${output}`);
    console.error(err.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
